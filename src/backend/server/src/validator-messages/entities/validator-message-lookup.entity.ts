import { Entity, Column } from 'typeorm';

/**
 * Lookup table for validator message types
 * Maps message type IDs to their string representations
 */
@Entity('validator_message_type_lu')
export class ValidatorMessageTypeLu {
  @Column({ name: 'message_type_id', type: 'smallint', primary: true })
  messageTypeId!: number;

  @Column({ name: 'message_type', type: 'text', unique: true })
  messageType!: string;
}

/**
 * Lookup table for validator processing statuses
 * Maps processing status IDs to their string representations
 */
@Entity('validator_processing_status_lu')
export class ValidatorProcessingStatusLu {
  @Column({ name: 'processing_status_id', type: 'smallint', primary: true })
  processingStatusId!: number;

  @Column({ name: 'processing_status', type: 'text', unique: true })
  processingStatus!: string;
}

/**
 * Constants for message type IDs based on the database schema
 * These should match the values in the validator_message_type_lu table
 */
export const MESSAGE_TYPE_IDS = {
  GPU_DEALLOCATION: 1,
  GPU_STATUS_CHANGE: 2,
  VALIDATOR_STATUS: 3,
  ALLOCATION_REQUEST: 4,
  POG_RESULT: 5,
  MINER_DISCOVERY: 6,
  MINER_ALLOCATION: 7,
  MINER_DEALLOCATION: 8,
} as const;

/**
 * Constants for processing status IDs based on the database schema
 * These should match the values in the validator_processing_status_lu table
 */
export const PROCESSING_STATUS_IDS = {
  PENDING: 1,
  PROCESSED: 2,
  FAILED: 3,
} as const;

/**
 * Type for message type ID values
 */
export type MessageTypeId = (typeof MESSAGE_TYPE_IDS)[keyof typeof MESSAGE_TYPE_IDS];

/**
 * Type for processing status ID values
 */
export type ProcessingStatusId = (typeof PROCESSING_STATUS_IDS)[keyof typeof PROCESSING_STATUS_IDS];
