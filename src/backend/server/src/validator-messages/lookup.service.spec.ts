import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MessageType } from '../pubsub/interfaces/pubsub-messages.interface';
import { ProcessingStatus } from './entities/validator-message.entity';
import { 
  ValidatorMessageTypeLu, 
  ValidatorProcessingStatusLu,
  MESSAGE_TYPE_IDS,
  PROCESSING_STATUS_IDS
} from './entities/validator-message-lookup.entity';
import { ValidatorMessageLookupService } from './lookup.service';

describe('ValidatorMessageLookupService', () => {
  let service: ValidatorMessageLookupService;
  let messageTypeLuRepository: Repository<ValidatorMessageTypeLu>;
  let processingStatusLuRepository: Repository<ValidatorProcessingStatusLu>;

  const mockMessageTypes: ValidatorMessageTypeLu[] = [
    { messageTypeId: 1, messageType: 'gpu_deallocation' },
    { messageTypeId: 2, messageType: 'gpu_status_change' },
    { messageTypeId: 3, messageType: 'pog_result' },
    { messageTypeId: 4, messageType: 'miner_allocation' },
    { messageTypeId: 5, messageType: 'miner_deallocation' },
  ];

  const mockProcessingStatuses: ValidatorProcessingStatusLu[] = [
    { processingStatusId: 1, processingStatus: 'pending' },
    { processingStatusId: 2, processingStatus: 'processed' },
    { processingStatusId: 3, processingStatus: 'failed' },
  ];

  const mockMessageTypeLuRepository = {
    find: jest.fn().mockResolvedValue(mockMessageTypes),
  };

  const mockProcessingStatusLuRepository = {
    find: jest.fn().mockResolvedValue(mockProcessingStatuses),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ValidatorMessageLookupService,
        {
          provide: getRepositoryToken(ValidatorMessageTypeLu),
          useValue: mockMessageTypeLuRepository,
        },
        {
          provide: getRepositoryToken(ValidatorProcessingStatusLu),
          useValue: mockProcessingStatusLuRepository,
        },
      ],
    }).compile();

    service = module.get<ValidatorMessageLookupService>(ValidatorMessageLookupService);
    messageTypeLuRepository = module.get<Repository<ValidatorMessageTypeLu>>(
      getRepositoryToken(ValidatorMessageTypeLu),
    );
    processingStatusLuRepository = module.get<Repository<ValidatorProcessingStatusLu>>(
      getRepositoryToken(ValidatorProcessingStatusLu),
    );

    // Initialize caches
    await service.onModuleInit();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('onModuleInit', () => {
    it('should initialize caches with lookup table data', async () => {
      expect(mockMessageTypeLuRepository.find).toHaveBeenCalled();
      expect(mockProcessingStatusLuRepository.find).toHaveBeenCalled();
    });
  });

  describe('getMessageTypeId', () => {
    it('should return correct ID for valid message type', () => {
      const result = service.getMessageTypeId(MessageType.GPU_DEALLOCATION);
      expect(result).toBe(1);
    });

    it('should return undefined for invalid message type', () => {
      const result = service.getMessageTypeId('invalid_type' as MessageType);
      expect(result).toBeUndefined();
    });
  });

  describe('getMessageTypeString', () => {
    it('should return correct string for valid ID', () => {
      const result = service.getMessageTypeString(1);
      expect(result).toBe('gpu_deallocation');
    });

    it('should return undefined for invalid ID', () => {
      const result = service.getMessageTypeString(999 as any);
      expect(result).toBeUndefined();
    });
  });

  describe('getProcessingStatusId', () => {
    it('should return correct ID for valid processing status', () => {
      const result = service.getProcessingStatusId(ProcessingStatus.PENDING);
      expect(result).toBe(1);
    });

    it('should return undefined for invalid processing status', () => {
      const result = service.getProcessingStatusId('invalid_status' as ProcessingStatus);
      expect(result).toBeUndefined();
    });
  });

  describe('getProcessingStatusString', () => {
    it('should return correct string for valid ID', () => {
      const result = service.getProcessingStatusString(2);
      expect(result).toBe('processed');
    });

    it('should return undefined for invalid ID', () => {
      const result = service.getProcessingStatusString(999 as any);
      expect(result).toBeUndefined();
    });
  });

  describe('mapMessageTypeToId', () => {
    it('should return ID for valid message type', () => {
      const result = service.mapMessageTypeToId(MessageType.POG_RESULT);
      expect(result).toBe(3);
    });

    it('should throw error for invalid message type', () => {
      expect(() => {
        service.mapMessageTypeToId('invalid_type' as MessageType);
      }).toThrow('Unknown message type: invalid_type');
    });
  });

  describe('mapProcessingStatusToId', () => {
    it('should return ID for valid processing status', () => {
      const result = service.mapProcessingStatusToId(ProcessingStatus.FAILED);
      expect(result).toBe(3);
    });

    it('should throw error for invalid processing status', () => {
      expect(() => {
        service.mapProcessingStatusToId('invalid_status' as ProcessingStatus);
      }).toThrow('Unknown processing status: invalid_status');
    });
  });

  describe('getAllMessageTypes', () => {
    it('should return all message types', () => {
      const result = service.getAllMessageTypes();
      expect(result).toHaveLength(5);
      expect(result).toEqual(expect.arrayContaining(mockMessageTypes));
    });
  });

  describe('getAllProcessingStatuses', () => {
    it('should return all processing statuses', () => {
      const result = service.getAllProcessingStatuses();
      expect(result).toHaveLength(3);
      expect(result).toEqual(expect.arrayContaining(mockProcessingStatuses));
    });
  });

  describe('hasMessageType', () => {
    it('should return true for existing message type', () => {
      const result = service.hasMessageType('gpu_deallocation');
      expect(result).toBe(true);
    });

    it('should return false for non-existing message type', () => {
      const result = service.hasMessageType('invalid_type');
      expect(result).toBe(false);
    });
  });

  describe('hasProcessingStatus', () => {
    it('should return true for existing processing status', () => {
      const result = service.hasProcessingStatus('pending');
      expect(result).toBe(true);
    });

    it('should return false for non-existing processing status', () => {
      const result = service.hasProcessingStatus('invalid_status');
      expect(result).toBe(false);
    });
  });

  describe('refreshCaches', () => {
    it('should clear and reinitialize caches', async () => {
      // Clear the mock call history
      jest.clearAllMocks();
      
      await service.refreshCaches();
      
      // Should call find again to reload data
      expect(mockMessageTypeLuRepository.find).toHaveBeenCalled();
      expect(mockProcessingStatusLuRepository.find).toHaveBeenCalled();
    });
  });

  describe('getMessageTypeLookup', () => {
    it('should return lookup entity for valid message type', () => {
      const result = service.getMessageTypeLookup(MessageType.GPU_STATUS_CHANGE);
      expect(result).toEqual({ messageTypeId: 2, messageType: 'gpu_status_change' });
    });

    it('should return undefined for invalid message type', () => {
      const result = service.getMessageTypeLookup('invalid_type' as MessageType);
      expect(result).toBeUndefined();
    });
  });

  describe('getProcessingStatusLookup', () => {
    it('should return lookup entity for valid processing status', () => {
      const result = service.getProcessingStatusLookup(ProcessingStatus.PROCESSED);
      expect(result).toEqual({ processingStatusId: 2, processingStatus: 'processed' });
    });

    it('should return undefined for invalid processing status', () => {
      const result = service.getProcessingStatusLookup('invalid_status' as ProcessingStatus);
      expect(result).toBeUndefined();
    });
  });
});
